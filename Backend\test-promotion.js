const mongoose = require('mongoose');
const Promotion = require('./src/models/Promotion');
const PromotionUser = require('./src/models/PromotionUser');

const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/uroom');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const testPromotionLogic = async () => {
  try {
    console.log('\n=== TESTING PROMOTION LOGIC ===\n');

    // Test 1: <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả promotions
    const allPromotions = await Promotion.find({});
    console.log(`Total promotions in database: ${allPromotions.length}`);
    
    allPromotions.forEach(promo => {
      console.log(`- ${promo.code} (${promo.type}): maxUsagePerUser=${promo.maxUsagePerUser}, active=${promo.isActive}`);
    });

    // Test 2: Ki<PERSON>m tra PromotionUser data cho từng user
    const testUsers = [11, 12, 13, 14, 15, 16];
    
    for (const userId of testUsers) {
      console.log(`\n--- USER ${userId} ---`);
      const userPromotions = await PromotionUser.find({ userId }).populate('promotionId');
      
      if (userPromotions.length === 0) {
        console.log('No promotions assigned');
      } else {
        userPromotions.forEach(up => {
          const promo = up.promotionId;
          const canUse = up.usedCount < up.maxUsagePerUser;
          console.log(`${promo.code} (${promo.type}): ${up.usedCount}/${up.maxUsagePerUser} - ${canUse ? 'CAN USE' : 'EXHAUSTED'}`);
        });
      }
    }

    // Test 3: Simulate API call logic
    console.log('\n=== SIMULATING API LOGIC ===\n');
    
    const simulateGetPromotions = async (userId) => {
      console.log(`\n--- Simulating getAllPromotions for User ${userId} ---`);
      
      // Lấy tất cả promotions active
      const allPromotions = await Promotion.find({ 
        isActive: true,
        endDate: { $gte: new Date() }
      });
      
      if (!userId) {
        // Guest user - chỉ thấy PUBLIC
        const publicPromotions = allPromotions.filter(p => p.type === 'PUBLIC');
        console.log(`Guest sees ${publicPromotions.length} PUBLIC promotions`);
        return;
      }
      
      // Logged in user
      const promotionUsers = await PromotionUser.find({ userId });
      const userPromotionMap = {};
      promotionUsers.forEach(pu => {
        userPromotionMap[String(pu.promotionId)] = pu;
      });
      
      const visiblePromotions = allPromotions
        .map(promotion => {
          const promotionId = String(promotion._id);
          const pu = userPromotionMap[promotionId];
          const usedCount = pu ? pu.usedCount : 0;
          const maxUsage = promotion.maxUsagePerUser || 1;
          
          let canUse = false;
          let shouldShow = false;
          
          if (promotion.type === 'PUBLIC') {
            shouldShow = true;
            canUse = usedCount < maxUsage;
          } else if (promotion.type === 'PRIVATE') {
            shouldShow = pu !== undefined;
            canUse = pu && usedCount < maxUsage;
          }
          
          return {
            code: promotion.code,
            type: promotion.type,
            userUsedCount: usedCount,
            maxUsage: maxUsage,
            canUse: canUse,
            shouldShow: shouldShow
          };
        })
        .filter(p => p.shouldShow);
      
      console.log(`User ${userId} sees ${visiblePromotions.length} promotions:`);
      visiblePromotions.forEach(p => {
        console.log(`  ${p.code} (${p.type}): ${p.userUsedCount}/${p.maxUsage} - ${p.canUse ? 'CAN USE' : 'EXHAUSTED'}`);
      });
    };
    
    // Test với các user khác nhau
    await simulateGetPromotions(null); // Guest
    await simulateGetPromotions(11);   // VIP user
    await simulateGetPromotions(12);   // Regular user
    await simulateGetPromotions(13);   // User with only 1 PRIVATE
    await simulateGetPromotions(16);   // User without PRIVATE promotions
    await simulateGetPromotions(99);   // User không có promotion nào

  } catch (error) {
    console.error('Error testing promotion logic:', error);
  }
};

const runTest = async () => {
  await connectDB();
  await testPromotionLogic();
  await mongoose.connection.close();
  console.log('\nTest completed');
};

runTest();
