const mongoose = require('mongoose');
const Promotion = require('./src/models/Promotion');
const PromotionUser = require('./src/models/PromotionUser');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/My_Uroom');
    console.log('MongoDB connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedPromotions = async () => {
  try {
    // Xóa dữ liệu cũ
    await Promotion.deleteMany({});
    await PromotionUser.deleteMany({});

    // Tạo promotions
    const promotions = await Promotion.insertMany([
      {
        code: "SUMMER10",
        name: "Summer Sale 10%",
        description: "Enjoy 10% off on all products during summer!",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 20,
        minOrderAmount: 100,
        startDate: new Date("2025-06-01"),
        endDate: new Date("2025-08-31"),
        usageLimit: 100,
        type: "PUBLIC",
        maxUsagePerUser: 2,
        isActive: true
      },
      {
        code: "VIP100",
        name: "VIP Member Exclusive",
        description: "Special $100 discount for VIP members only",
        discountType: "FIXED_AMOUNT",
        discountValue: 100,
        minOrderAmount: 500,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 10,
        type: "PRIVATE",
        maxUsagePerUser: 1,
        isActive: true
      },
      {
        code: "WELCOME50",
        name: "Welcome Bonus",
        description: "Get $50 off for your first order!",
        discountType: "FIXED_AMOUNT",
        discountValue: 50,
        minOrderAmount: 200,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: null,
        type: "PUBLIC",
        maxUsagePerUser: 1,
        isActive: true
      },
      {
        code: "REPEAT20",
        name: "Repeat Customer",
        description: "20% off for returning customers",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 100,
        minOrderAmount: 150,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 200,
        type: "PUBLIC",
        maxUsagePerUser: 3,
        isActive: true
      }
    ]);

    // Tạo dữ liệu PromotionUser cho các user test (id từ 11 trở lên)
    const promotionUserData = [
      // User 11 - VIP user, đã dùng hết lượt WELCOME50
      {
        promotionId: promotions[1]._id, // VIP100
        userId: 11,
        usedCount: 0, // Còn lượt
        maxUsagePerUser: promotions[1].maxUsagePerUser,
      },
      {
        promotionId: promotions[2]._id, // WELCOME50
        userId: 11,
        usedCount: 1, // Đã hết lượt (maxUsagePerUser = 1)
        maxUsagePerUser: promotions[2].maxUsagePerUser,
      },
      {
        promotionId: promotions[3]._id, // REPEAT20
        userId: 11,
        usedCount: 1, // Còn 2 lượt (maxUsagePerUser = 3)
        maxUsagePerUser: promotions[3].maxUsagePerUser,
      },

      // User 12 - Đã dùng hết lượt SUMMER10
      {
        promotionId: promotions[0]._id, // SUMMER10
        userId: 12,
        usedCount: 2, // Đã hết lượt (maxUsagePerUser = 2)
        maxUsagePerUser: promotions[0].maxUsagePerUser,
      },
      {
        promotionId: promotions[2]._id, // WELCOME50
        userId: 12,
        usedCount: 0, // Còn lượt
        maxUsagePerUser: promotions[2].maxUsagePerUser,
      },

      // User 13 - Mới, chưa dùng promotion nào

      // User 14 - Đã dùng 1 lượt REPEAT20
      {
        promotionId: promotions[3]._id, // REPEAT20
        userId: 14,
        usedCount: 1, // Còn 2 lượt
        maxUsagePerUser: promotions[3].maxUsagePerUser,
      },

      // User 15 - Đã dùng hết lượt WELCOME50 và 1 lượt SUMMER10
      {
        promotionId: promotions[2]._id, // WELCOME50
        userId: 15,
        usedCount: 1, // Đã hết lượt
        maxUsagePerUser: promotions[2].maxUsagePerUser,
      },
      {
        promotionId: promotions[0]._id, // SUMMER10
        userId: 15,
        usedCount: 1, // Còn 1 lượt
        maxUsagePerUser: promotions[0].maxUsagePerUser,
      }
    ];

    await PromotionUser.insertMany(promotionUserData);

    console.log('Promotions seeded successfully');
  } catch (error) {
    console.error('Error seeding promotions:', error);
  }
};

const runSeed = async () => {
  await connectDB();
  await seedPromotions();
  await mongoose.connection.close();
  console.log('Seed completed');
};

runSeed();
