const mongoose = require('mongoose');
const Promotion = require('./src/models/Promotion');
const PromotionUser = require('./src/models/PromotionUser');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/My_Uroom');
    console.log('MongoDB connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedPromotions = async () => {
  try {
    // Xóa dữ liệu cũ
    await Promotion.deleteMany({});
    await PromotionUser.deleteMany({});

    // Tạo promotions
    const promotions = await Promotion.insertMany([
      {
        code: "SUMMER10",
        name: "Summer Sale 10%",
        description: "Enjoy 10% off on all products during summer!",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 20,
        minOrderAmount: 100,
        startDate: new Date("2025-06-01"),
        endDate: new Date("2025-08-31"),
        usageLimit: 100,
        type: "PUBLIC",
        maxUsagePerUser: 2,
        isActive: true
      },
      {
        code: "VIP100",
        name: "VIP Member Exclusive",
        description: "Special $100 discount for VIP members only",
        discountType: "FIXED_AMOUNT",
        discountValue: 100,
        minOrderAmount: 500,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 10,
        type: "PRIVATE",
        maxUsagePerUser: 1,
        isActive: true
      },
      {
        code: "WELCOME50",
        name: "Welcome Bonus",
        description: "Get $50 off for your first order!",
        discountType: "FIXED_AMOUNT",
        discountValue: 50,
        minOrderAmount: 200,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: null,
        type: "PUBLIC",
        maxUsagePerUser: 1,
        isActive: true
      },
      {
        code: "REPEAT20",
        name: "Repeat Customer",
        description: "20% off for returning customers",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 100,
        minOrderAmount: 150,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 200,
        type: "PUBLIC",
        maxUsagePerUser: 3,
        isActive: true
      },
      {
        code: "STUDENT15",
        name: "Student Discount",
        description: "15% off for students",
        discountType: "PERCENTAGE",
        discountValue: 15,
        maxDiscountAmount: 50,
        minOrderAmount: 100,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: null,
        type: "PRIVATE",
        maxUsagePerUser: 2,
        isActive: true
      },
      {
        code: "LOYALTY25",
        name: "Loyalty Member",
        description: "25% off for loyalty members",
        discountType: "PERCENTAGE",
        discountValue: 25,
        maxDiscountAmount: 200,
        minOrderAmount: 300,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 50,
        type: "PRIVATE",
        maxUsagePerUser: 5,
        isActive: true
      },
      {
        code: "WEEKEND10",
        name: "Weekend Special",
        description: "10% off on weekends",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 30,
        minOrderAmount: 80,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        type: "PUBLIC",
        maxUsagePerUser: 4,
        isActive: true
      },
      {
        code: "EXPIRED20",
        name: "Expired Promotion",
        description: "This promotion has expired",
        discountType: "PERCENTAGE",
        discountValue: 20,
        minOrderAmount: 100,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-12-31"), // Đã hết hạn
        usageLimit: 100,
        type: "PUBLIC",
        maxUsagePerUser: 2,
        isActive: true
      },
      {
        code: "INACTIVE30",
        name: "Inactive Promotion",
        description: "This promotion is inactive",
        discountType: "PERCENTAGE",
        discountValue: 30,
        minOrderAmount: 150,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        type: "PUBLIC",
        maxUsagePerUser: 1,
        isActive: false // Không active
      }
    ]);

    // Tạo dữ liệu PromotionUser cho các user test (id từ 11 trở lên)
    const promotionUserData = [
      // User 11 - VIP user với nhiều promotion để test đầy đủ
      {
        promotionId: promotions[1]._id, // VIP100 (PRIVATE)
        userId: 11,
        usedCount: 0, // Còn lượt
        maxUsagePerUser: promotions[1].maxUsagePerUser,
      },
      {
        promotionId: promotions[2]._id, // WELCOME50 (PUBLIC)
        userId: 11,
        usedCount: 1, // Đã hết lượt (maxUsagePerUser = 1)
        maxUsagePerUser: promotions[2].maxUsagePerUser,
      },
      {
        promotionId: promotions[3]._id, // REPEAT20 (PUBLIC)
        userId: 11,
        usedCount: 1, // Còn 2 lượt (maxUsagePerUser = 3)
        maxUsagePerUser: promotions[3].maxUsagePerUser,
      },
      {
        promotionId: promotions[4]._id, // STUDENT15 (PRIVATE)
        userId: 11,
        usedCount: 0, // Còn 2 lượt
        maxUsagePerUser: promotions[4].maxUsagePerUser,
      },
      {
        promotionId: promotions[5]._id, // LOYALTY25 (PRIVATE)
        userId: 11,
        usedCount: 2, // Còn 3 lượt (maxUsagePerUser = 5)
        maxUsagePerUser: promotions[5].maxUsagePerUser,
      },
      {
        promotionId: promotions[6]._id, // WEEKEND10 (PUBLIC)
        userId: 11,
        usedCount: 4, // Đã hết lượt (maxUsagePerUser = 4)
        maxUsagePerUser: promotions[6].maxUsagePerUser,
      },

      // User 12 - Đã dùng hết lượt SUMMER10, có một số promotion khác
      {
        promotionId: promotions[0]._id, // SUMMER10 (PUBLIC)
        userId: 12,
        usedCount: 2, // Đã hết lượt (maxUsagePerUser = 2)
        maxUsagePerUser: promotions[0].maxUsagePerUser,
      },
      {
        promotionId: promotions[2]._id, // WELCOME50 (PUBLIC)
        userId: 12,
        usedCount: 0, // Còn lượt
        maxUsagePerUser: promotions[2].maxUsagePerUser,
      },
      {
        promotionId: promotions[4]._id, // STUDENT15 (PRIVATE)
        userId: 12,
        usedCount: 1, // Còn 1 lượt
        maxUsagePerUser: promotions[4].maxUsagePerUser,
      },

      // User 13 - Mới, chỉ có 1 PRIVATE promotion
      {
        promotionId: promotions[5]._id, // LOYALTY25 (PRIVATE)
        userId: 13,
        usedCount: 0, // Chưa dùng
        maxUsagePerUser: promotions[5].maxUsagePerUser,
      },

      // User 14 - Đã dùng 1 lượt REPEAT20, có STUDENT15
      {
        promotionId: promotions[3]._id, // REPEAT20 (PUBLIC)
        userId: 14,
        usedCount: 1, // Còn 2 lượt
        maxUsagePerUser: promotions[3].maxUsagePerUser,
      },
      {
        promotionId: promotions[4]._id, // STUDENT15 (PRIVATE)
        userId: 14,
        usedCount: 2, // Đã hết lượt (maxUsagePerUser = 2)
        maxUsagePerUser: promotions[4].maxUsagePerUser,
      },

      // User 15 - Đã dùng hết lượt WELCOME50 và 1 lượt SUMMER10
      {
        promotionId: promotions[2]._id, // WELCOME50 (PUBLIC)
        userId: 15,
        usedCount: 1, // Đã hết lượt
        maxUsagePerUser: promotions[2].maxUsagePerUser,
      },
      {
        promotionId: promotions[0]._id, // SUMMER10 (PUBLIC)
        userId: 15,
        usedCount: 1, // Còn 1 lượt
        maxUsagePerUser: promotions[0].maxUsagePerUser,
      },

      // User 16 - Không có PRIVATE promotion nào (để test user không thấy PRIVATE)
      {
        promotionId: promotions[0]._id, // SUMMER10 (PUBLIC)
        userId: 16,
        usedCount: 0, // Còn lượt
        maxUsagePerUser: promotions[0].maxUsagePerUser,
      },
      {
        promotionId: promotions[6]._id, // WEEKEND10 (PUBLIC)
        userId: 16,
        usedCount: 1, // Còn 3 lượt
        maxUsagePerUser: promotions[6].maxUsagePerUser,
      }
    ];

    await PromotionUser.insertMany(promotionUserData);

    console.log('Promotions seeded successfully');
    console.log('\n=== TEST CASES CREATED ===');
    console.log('User 11 (VIP): VIP100✓, WELCOME50✗, REPEAT20✓, STUDENT15✓, LOYALTY25✓, WEEKEND10✗');
    console.log('User 12: SUMMER10✗, WELCOME50✓, STUDENT15✓');
    console.log('User 13: LOYALTY25✓ (chỉ có 1 PRIVATE promotion)');
    console.log('User 14: REPEAT20✓, STUDENT15✗');
    console.log('User 15: WELCOME50✗, SUMMER10✓');
    console.log('User 16: SUMMER10✓, WEEKEND10✓ (không có PRIVATE promotion)');
    console.log('\n✓ = Còn lượt dùng, ✗ = Đã hết lượt');
    console.log('PRIVATE promotions: VIP100, STUDENT15, LOYALTY25');
    console.log('PUBLIC promotions: SUMMER10, WELCOME50, REPEAT20, WEEKEND10');
    console.log('EXPIRED: EXPIRED20, INACTIVE: INACTIVE30');
  } catch (error) {
    console.error('Error seeding promotions:', error);
  }
};

const runSeed = async () => {
  await connectDB();
  await seedPromotions();
  await mongoose.connection.close();
  console.log('Seed completed');
};

runSeed();
