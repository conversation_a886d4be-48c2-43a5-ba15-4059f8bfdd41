const Promotion = require('../../models/Promotion');
const PromotionUser = require('../../models/PromotionUser');

// Create new promotion
exports.createPromotion = async (req, res) => {
  try {
    const promotion = new Promotion({ ...req.body, createdBy: req.user._id });
    await promotion.save();
    res.status(201).json(promotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get all promotions with pagination
exports.getAllPromotions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status; // 'active', 'inactive', 'expired', 'upcoming', 'all'
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Build filter object
    let filter = {};

    // Search filter
    if (search) {
      filter.$or = [
        { code: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    const now = new Date();
    if (status === 'active') {
      filter.isActive = true;
      filter.startDate = { $lte: now };
      filter.endDate = { $gte: now };
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.endDate = { $lt: now };
    } else if (status === 'upcoming') {
      filter.isActive = true;
      filter.startDate = { $gt: now };
    } else if (status === 'all') {
      // Không filter gì cả - hiển thị tất cả
    } else {
      // Mặc định chỉ hiển thị promotion active và chưa hết hạn
      filter.isActive = true;
      filter.endDate = { $gte: now };
    }

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Get all promotions (for filtering by user below)
    let allPromotions = await Promotion.find(filter)
      .sort({ [sortBy]: sortOrder });

    // Lọc promotion dựa trên user và maxUsagePerUser
    const userId = req.user?.id;

    let promotions = allPromotions;

    if (userId) {
      // Lấy tất cả PromotionUser records của user này
      const promotionUsers = await PromotionUser.find({ userId });

      // Tạo map để tra cứu nhanh
      const userPromotionMap = {};
      promotionUsers.forEach(pu => {
        userPromotionMap[String(pu.promotionId)] = pu;
      });

      // Lọc và thêm thông tin cho từng promotion
      promotions = allPromotions
        .map(promotion => {
          const promotionId = String(promotion._id);
          const pu = userPromotionMap[promotionId];
          const usedCount = pu ? pu.usedCount : 0;
          const maxUsage = promotion.maxUsagePerUser || 1;

          // Tính toán canUse dựa trên type và usage
          let canUse = false;
          let shouldShow = false;

          if (promotion.type === 'PUBLIC') {
            // PUBLIC: Hiển thị cho tất cả user, có thể dùng nếu chưa hết lượt
            shouldShow = true;
            canUse = usedCount < maxUsage;
          } else if (promotion.type === 'PRIVATE') {
            // PRIVATE: Chỉ hiển thị cho user đã được gán, có thể dùng nếu chưa hết lượt
            shouldShow = pu !== undefined;
            canUse = pu && usedCount < maxUsage;
          }

          return {
            ...promotion.toObject(),
            userUsedCount: usedCount,
            canUse: canUse,
            shouldShow: shouldShow
          };
        })
        .filter(promotion => promotion.shouldShow); // Chỉ trả về promotion mà user được phép xem
    } else {
      // Nếu không có userId (không đăng nhập), chỉ trả về PUBLIC promotions
      promotions = allPromotions
        .filter(promotion => promotion.type === 'PUBLIC')
        .map(promotion => ({
          ...promotion.toObject(),
          userUsedCount: 0,
          canUse: true // Guest có thể xem nhưng không thể apply
        }));
    }

    // Pagination
    const totalPromotions = promotions.length;
    const totalPages = Math.ceil(totalPromotions / limit);
    const paginatedPromotions = promotions.slice(skip, skip + limit);

    // Calculate statistics
    const stats = {
      total: allPromotions.length,
      active: allPromotions.filter(p => p.isActive && p.startDate <= now && p.endDate >= now).length,
      inactive: allPromotions.filter(p => !p.isActive).length,
      expired: allPromotions.filter(p => p.endDate < now).length,
      upcoming: allPromotions.filter(p => p.isActive && p.startDate > now).length
    };

    res.json({
      promotions: paginatedPromotions,
      pagination: {
        currentPage: page,
        totalPages,
        totalPromotions,
        limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      stats,
      filters: {
        search,
        status,
        sortBy,
        sortOrder: req.query.sortOrder || 'desc'
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get promotion by ID
exports.getPromotionById = async (req, res) => {
  try {
    const promotion = await Promotion.findById(req.params.id);
    if (!promotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(promotion);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update promotion
exports.updatePromotion = async (req, res) => {
  try {
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete promotion
exports.deletePromotion = async (req, res) => {
  try {
    const deleted = await Promotion.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ message: 'Promotion not found' });
    res.json({ message: 'Promotion deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Toggle promotion status
exports.togglePromotionStatus = async (req, res) => {
  try {
    const { isActive } = req.body;
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Apply promotion code
exports.applyPromotionCode = async (req, res) => {
  try {
    const { code, orderAmount } = req.body;
    const userId = req.user?.id; // Lấy userId từ token nếu có

    const promotion = await Promotion.findOne({ code: code.toUpperCase(), isActive: true });

    if (!promotion) return res.status(404).json({ message: 'Invalid or inactive promotion code' });

    const now = new Date();
    if (now < promotion.startDate || now > promotion.endDate) {
      return res.status(400).json({ message: 'Promotion is not active at this time' });
    }

    if (promotion.usageLimit && promotion.usedCount >= promotion.usageLimit) {
      return res.status(400).json({ message: 'Promotion usage limit exceeded' });
    }

    if (orderAmount < promotion.minOrderAmount) {
      return res.status(400).json({ message: `Minimum order amount is ${promotion.minOrderAmount}` });
    }

    // Kiểm tra quyền sử dụng promotion và số lượt đã dùng
    if (!userId) {
      return res.status(401).json({ message: 'User authentication required' });
    }

    // Tìm PromotionUser record
    const promotionUser = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId: userId
    });

    const userUsedCount = promotionUser ? promotionUser.usedCount : 0;
    const maxUsage = promotion.maxUsagePerUser || 1;

    // Kiểm tra quyền sử dụng dựa trên type
    if (promotion.type === 'PRIVATE' && !promotionUser) {
      return res.status(403).json({
        message: 'This is a private promotion. You are not authorized to use it.'
      });
    }

    // Kiểm tra số lượt đã sử dụng
    if (userUsedCount >= maxUsage) {
      return res.status(400).json({
        message: `You have reached the maximum usage limit (${maxUsage}) for this promotion`
      });
    }

    // Tự động tạo PromotionUser cho PUBLIC promotion nếu chưa có
    if (promotion.type === 'PUBLIC' && !promotionUser) {
      await PromotionUser.create({
        promotionId: promotion._id,
        userId: userId,
        usedCount: 0,
        maxUsagePerUser: maxUsage
      });
    }

    let discount = 0;
    if (promotion.discountType === 'PERCENTAGE') {
      discount = (orderAmount * promotion.discountValue) / 100;
      if (promotion.maxDiscountAmount) {
        discount = Math.min(discount, promotion.maxDiscountAmount);
      }
    } else if (promotion.discountType === 'FIXED_AMOUNT') {
      discount = promotion.discountValue;
    }

    res.json({
      valid: true,
      discount,
      message: 'Promotion applied successfully',
      promotionId: promotion._id
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
