const Promotion = require('../../models/Promotion');
const PromotionUser = require('../../models/PromotionUser');

// Create new promotion
exports.createPromotion = async (req, res) => {
  try {
    const promotion = new Promotion({ ...req.body, createdBy: req.user._id });
    await promotion.save();
    res.status(201).json(promotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get all promotions with pagination
exports.getAllPromotions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status; // 'active', 'inactive', 'expired', 'upcoming', 'all'
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Build filter object
    let filter = {};

    // Search filter
    if (search) {
      filter.$or = [
        { code: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    const now = new Date();
    if (status === 'active') {
      filter.isActive = true;
      filter.startDate = { $lte: now };
      filter.endDate = { $gte: now };
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.endDate = { $lt: now };
    } else if (status === 'upcoming') {
      filter.isActive = true;
      filter.startDate = { $gt: now };
    }

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Get all promotions (for filtering by user below)
    let allPromotions = await Promotion.find(filter)
      .sort({ [sortBy]: sortOrder });

    // Nếu có userId (customer request), lọc promotion dựa trên số lượt đã sử dụng và loại promotion
    const userId = req.user?.id;
    let promotions = allPromotions;
    if (userId) {
      const promotionUsers = await PromotionUser.find({ userId });
      const userPromotionUsage = {};
      promotionUsers.forEach(pu => {
        userPromotionUsage[String(pu.promotionId)] = pu;
      });
      // Log để debug mapping
      console.log('allPromotions:', allPromotions.map(p => ({
        _id: String(p._id),
        code: p.code,
        maxUsagePerUser: p.maxUsagePerUser
      })));
      console.log('userPromotionUsage:', userPromotionUsage);
      // Lọc promotion dựa trên loại và số lượt đã sử dụng
      promotions = allPromotions.filter(promotion => {
        const pu = userPromotionUsage[String(promotion._id)];
        const usedCount = pu ? pu.usedCount : 0;
        const maxUsage = promotion.maxUsagePerUser || 1;

        // Với promotion PUBLIC: user có thể dùng ngay cả khi chưa có record
        // Với promotion PRIVATE: user phải được gán trước
        if (promotion.type === 'PUBLIC') {
          return usedCount < maxUsage;
        } else if (promotion.type === 'PRIVATE') {
          return pu && usedCount < maxUsage;
        }

        return false;
      });
      // Thêm thông tin về số lượt đã sử dụng, còn dùng được không, loại promotion
      promotions = promotions.map(promotion => {
        const pu = userPromotionUsage[String(promotion._id)];
        const usedCount = pu ? pu.usedCount : 0;
        const maxUsage = promotion.maxUsagePerUser || 1;

        // Với promotion PUBLIC: user có thể dùng ngay cả khi chưa có record trong PromotionUser
        // Với promotion PRIVATE: user phải được gán trước (có record trong PromotionUser)
        let canUse = false;
        if (promotion.type === 'PUBLIC') {
          canUse = usedCount < maxUsage;
        } else if (promotion.type === 'PRIVATE') {
          canUse = pu && usedCount < maxUsage;
        }

        return {
          ...promotion.toObject(),
          userUsedCount: usedCount,
          canUse: canUse
        };
      });
    }

    // Pagination
    const totalPromotions = promotions.length;
    const totalPages = Math.ceil(totalPromotions / limit);
    const paginatedPromotions = promotions.slice(skip, skip + limit);

    // Calculate statistics
    const stats = {
      total: allPromotions.length,
      active: allPromotions.filter(p => p.isActive && p.startDate <= now && p.endDate >= now).length,
      inactive: allPromotions.filter(p => !p.isActive).length,
      expired: allPromotions.filter(p => p.endDate < now).length,
      upcoming: allPromotions.filter(p => p.isActive && p.startDate > now).length
    };

    res.json({
      promotions: paginatedPromotions,
      pagination: {
        currentPage: page,
        totalPages,
        totalPromotions,
        limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      stats,
      filters: {
        search,
        status,
        sortBy,
        sortOrder: req.query.sortOrder || 'desc'
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get promotion by ID
exports.getPromotionById = async (req, res) => {
  try {
    const promotion = await Promotion.findById(req.params.id);
    if (!promotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(promotion);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update promotion
exports.updatePromotion = async (req, res) => {
  try {
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete promotion
exports.deletePromotion = async (req, res) => {
  try {
    const deleted = await Promotion.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ message: 'Promotion not found' });
    res.json({ message: 'Promotion deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Toggle promotion status
exports.togglePromotionStatus = async (req, res) => {
  try {
    const { isActive } = req.body;
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Apply promotion code
exports.applyPromotionCode = async (req, res) => {
  try {
    const { code, orderAmount } = req.body;
    const userId = req.user?.id; // Lấy userId từ token nếu có

    const promotion = await Promotion.findOne({ code: code.toUpperCase(), isActive: true });

    if (!promotion) return res.status(404).json({ message: 'Invalid or inactive promotion code' });

    const now = new Date();
    if (now < promotion.startDate || now > promotion.endDate) {
      return res.status(400).json({ message: 'Promotion is not active at this time' });
    }

    if (promotion.usageLimit && promotion.usedCount >= promotion.usageLimit) {
      return res.status(400).json({ message: 'Promotion usage limit exceeded' });
    }

    if (orderAmount < promotion.minOrderAmount) {
      return res.status(400).json({ message: `Minimum order amount is ${promotion.minOrderAmount}` });
    }

    // Kiểm tra số lượt đã sử dụng của user với promotion này
    let promotionUser = null;
    let userUsedCount = 0;
    if (userId && promotion.maxUsagePerUser) {
      promotionUser = await PromotionUser.findOne({
        promotionId: promotion._id,
        userId: userId
      });
      userUsedCount = promotionUser ? promotionUser.usedCount : 0;
      // Nếu là private và user chưa có, tự động gán promotion cho user
      if (promotion.type === 'PRIVATE' && !promotionUser) {
        promotionUser = await PromotionUser.create({
          promotionId: promotion._id,
          userId: userId,
          usedCount: 0,
          maxUsagePerUser: promotion.maxUsagePerUser
        });
        userUsedCount = 0;
      }
      // Nếu là public, chỉ cho dùng nếu còn lượt
      if (userUsedCount >= promotion.maxUsagePerUser) {
        return res.status(400).json({
          message: `You have reached the maximum usage limit (${promotion.maxUsagePerUser}) for this promotion`
        });
      }
    }

    let discount = 0;
    if (promotion.discountType === 'PERCENTAGE') {
      discount = (orderAmount * promotion.discountValue) / 100;
      if (promotion.maxDiscountAmount) {
        discount = Math.min(discount, promotion.maxDiscountAmount);
      }
    } else if (promotion.discountType === 'FIXED_AMOUNT') {
      discount = promotion.discountValue;
    }

    res.json({
      valid: true,
      discount,
      message: 'Promotion applied successfully',
      promotionId: promotion._id
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
