const mongoose = require('mongoose');

const promotionUserSchema = new mongoose.Schema(
  {
    promotionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Promotion',
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    usedCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    maxUsagePerUser: {
      type: Number,
      default: 1,
      min: 1,
      required: true,
    },
  },
  { 
    versionKey: false,
    timestamps: true 
  }
);

// Tạo compound index để đảm bảo mỗi user chỉ có 1 record cho mỗi promotion
promotionUserSchema.index({ promotionId: 1, userId: 1 }, { unique: true });

module.exports = mongoose.model('PromotionUser', promotionUserSchema);
